/**
 * 智能体状态管理模块
 */

import {
  mockApiCall
} from '@/api/aiAgent';

// 导入智能体配置
import { getAiAgentUrl, AI_AGENT_CONFIG, AI_AGENT_USER_PREFERENCES } from '@/config/aiAgent';

const state = {
  // 智能体功能是否可用
  isAiAgentAvailable: false,

  // 是否显示智能体区域
  showAiAgent: false,

  // 智能体加载状态
  aiAgentLoading: false,

  // 智能体错误状态
  aiAgentError: false,

  // 智能体页面URL
  aiAgentUrl: '',

  // 智能体配置信息
  aiAgentConfig: null,

  // 用户偏好设置（使用配置文件中的默认值）
  userPreferences: { ...AI_AGENT_USER_PREFERENCES },

  // 当前路由信息
  currentRoute: null,

  // 支持智能体的专业类型列表
  supportedProfessions: [31], // 31为核心网专业

  // ========== 新增：用户真实展开意图记录 ==========
  // 记录用户最后一次主动操作的智能体状态（不受页面失活等系统行为影响）
  userLastIntentionState: false,

  // ========== 新增：多工单消息过滤机制 ==========
  // 当前活跃的工单信息
  currentActiveWorkOrder: {
    woId: null,           // 工单ID
    sheetNo: null,        // 工单编号
    pageInstanceId: null, // 页面实例ID（用于区分同一工单的多个页面实例）
  },

  // 智能体消息目标工单信息
  aiAgentTargetWorkOrder: {
    woId: null,
    sheetNo: null,
  },
};

const mutations = {
  // 设置智能体可用性
  SET_AI_AGENT_AVAILABLE(state, available) {
    state.isAiAgentAvailable = available;
  },

  // 设置显示状态
  SET_SHOW_AI_AGENT(state, show) {
    state.showAiAgent = show;

    // 如果启用了记住状态，保存到localStorage
    if (state.userPreferences.rememberState) {
      localStorage.setItem('aiAgent_showState', JSON.stringify(show));
    }
  },

  // 设置加载状态
  SET_AI_AGENT_LOADING(state, loading) {
    state.aiAgentLoading = loading;
  },

  // 设置错误状态
  SET_AI_AGENT_ERROR(state, error) {
    state.aiAgentError = error;
  },

  // 设置智能体URL
  SET_AI_AGENT_URL(state, url) {
    state.aiAgentUrl = url;
  },

  // 设置智能体配置
  SET_AI_AGENT_CONFIG(state, config) {
    state.aiAgentConfig = config;
  },

  // 更新用户偏好
  UPDATE_USER_PREFERENCES(state, preferences) {
    state.userPreferences = { ...state.userPreferences, ...preferences };
    localStorage.setItem('aiAgent_preferences', JSON.stringify(state.userPreferences));
  },

  // 设置当前路由
  SET_CURRENT_ROUTE(state, route) {
    state.currentRoute = route;
  },

  // 更新支持的专业类型
  UPDATE_SUPPORTED_PROFESSIONS(state, professions) {
    state.supportedProfessions = professions;
  },

  // ========== 新增：多工单消息过滤相关mutations ==========
  // 设置当前活跃工单
  SET_CURRENT_ACTIVE_WORK_ORDER(state, workOrderInfo) {
    state.currentActiveWorkOrder = {
      woId: workOrderInfo.woId || null,
      sheetNo: workOrderInfo.sheetNo || null,
      pageInstanceId: workOrderInfo.pageInstanceId || null,
    };
  },

  // 设置智能体消息目标工单
  SET_AI_AGENT_TARGET_WORK_ORDER(state, workOrderInfo) {
    state.aiAgentTargetWorkOrder = {
      woId: workOrderInfo.woId || null,
      sheetNo: workOrderInfo.sheetNo || null,
    };
  },

  // 清除当前活跃工单
  CLEAR_CURRENT_ACTIVE_WORK_ORDER(state) {
    state.currentActiveWorkOrder = {
      woId: null,
      sheetNo: null,
      pageInstanceId: null,
    };
  },

  // ========== 新增：用户真实意图记录相关mutations ==========
  // 设置用户最后一次主动操作的智能体状态（仅在用户点击按钮时调用）
  SET_USER_LAST_INTENTION_STATE(state, intentionState) {
    state.userLastIntentionState = intentionState;
    console.log('🤖 记录用户真实意图状态:', intentionState);
  },
};

const actions = {
  // 初始化智能体模块
  async initAiAgent({ commit, dispatch }, route) {
    commit('SET_CURRENT_ROUTE', route);

    // 恢复用户偏好设置
    const savedPreferences = localStorage.getItem('aiAgent_preferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        commit('UPDATE_USER_PREFERENCES', preferences);
      } catch (error) {
        console.warn('Failed to parse AI agent preferences:', error);
      }
    }

    // 检查智能体可用性
    await dispatch('checkAiAgentAvailability', route);

    // 恢复显示状态
    const savedShowState = localStorage.getItem('aiAgent_showState');
    if (savedShowState && state.userPreferences.rememberState) {
      try {
        const showState = JSON.parse(savedShowState);
        if (showState && state.isAiAgentAvailable) {
          commit('SET_SHOW_AI_AGENT', true);
          await dispatch('loadAiAgent');
        }
      } catch (error) {
        console.warn('Failed to parse AI agent show state:', error);
      }
    }
  },

  // 检查智能体功能可用性
  async checkAiAgentAvailability({ commit, state }, route) {
    try {
      // 检查当前页面是否支持智能体功能
      const isSupported = await checkPageSupport(route);

      if (isSupported) {
        // 检查专业类型是否支持
        const professionSupported = await checkProfessionSupport(route);
        commit('SET_AI_AGENT_AVAILABLE', professionSupported);
      } else {
        commit('SET_AI_AGENT_AVAILABLE', false);
      }
    } catch (error) {
      console.error('Failed to check AI agent availability:', error);
      commit('SET_AI_AGENT_AVAILABLE', false);
    }
  },

  // 切换智能体显示状态
  async toggleAiAgent({ commit, state, dispatch }) {
    if (!state.isAiAgentAvailable) {
      return;
    }

    const newShowState = !state.showAiAgent;

    if (newShowState && !state.aiAgentUrl) {
      // 如果要显示但还没有URL，先加载
      await dispatch('loadAiAgent');
    }

    commit('SET_SHOW_AI_AGENT', newShowState);
  },

  // 显示智能体
  async showAiAgent({ commit, state, dispatch }) {
    if (!state.isAiAgentAvailable) {
      return;
    }

    if (!state.aiAgentUrl) {
      await dispatch('loadAiAgent');
    }

    commit('SET_SHOW_AI_AGENT', true);
  },

  // 隐藏智能体
  hideAiAgent({ commit }) {
    commit('SET_SHOW_AI_AGENT', false);
  },

  // 加载智能体
  async loadAiAgent({ commit, state }) {
    if (state.aiAgentLoading) {
      return;
    }

    commit('SET_AI_AGENT_LOADING', true);
    commit('SET_AI_AGENT_ERROR', false);

    try {
      // 使用固定的智能体URL和配置
      const result = await loadAiAgentConfig(state.currentRoute);

      commit('SET_AI_AGENT_URL', result.url);
      commit('SET_AI_AGENT_CONFIG', result.config);
    } catch (error) {
      console.error('Failed to load AI agent:', error);
      commit('SET_AI_AGENT_ERROR', true);
    } finally {
      commit('SET_AI_AGENT_LOADING', false);
    }
  },

  // 重新加载智能体
  async reloadAiAgent({ commit, dispatch }) {
    commit('SET_AI_AGENT_URL', '');
    commit('SET_AI_AGENT_CONFIG', null);
    await dispatch('loadAiAgent');
  },

  // 更新智能体数据
  async updateAiAgentData({ commit, state }, { route, workOrderData, urlParams }) {
    try {
      // 更新当前路由
      commit('SET_CURRENT_ROUTE', route);

      // 检查智能体可用性
      await this.dispatch('aiAgent/checkAiAgentAvailability', route);

      if (state.isAiAgentAvailable) {
        // 使用配置文件中的智能体URL（支持环境自动切换）
        const aiAgentUrl = getAiAgentUrl();

        // 更新智能体URL和配置
        commit('SET_AI_AGENT_URL', aiAgentUrl);
        commit('SET_AI_AGENT_CONFIG', AI_AGENT_CONFIG);

        console.log('🤖 全局智能体数据已更新:', {
          url: aiAgentUrl,
          workOrderData,
          environment: process.env.NODE_ENV,
          note: '智能体URL根据环境自动选择，详见 src/config/aiAgent.js'
        });
      }
    } catch (error) {
      console.error('Failed to update AI agent data:', error);
      commit('SET_AI_AGENT_ERROR', true);
    }
  },

  // 更新用户偏好
  updatePreferences({ commit }, preferences) {
    commit('UPDATE_USER_PREFERENCES', preferences);
  },

  // 清理智能体状态
  clearAiAgent({ commit }) {
    commit('SET_SHOW_AI_AGENT', false);
    commit('SET_AI_AGENT_URL', '');
    commit('SET_AI_AGENT_CONFIG', null);
    commit('SET_AI_AGENT_LOADING', false);
    commit('SET_AI_AGENT_ERROR', false);
  },

  // ========== 新增：多工单消息过滤相关actions ==========
  // 设置当前活跃工单
  setCurrentActiveWorkOrder({ commit }, workOrderInfo) {
    commit('SET_CURRENT_ACTIVE_WORK_ORDER', workOrderInfo);
    console.log('🤖 设置当前活跃工单:', workOrderInfo);
  },

  // 设置智能体消息目标工单
  setAiAgentTargetWorkOrder({ commit }, workOrderInfo) {
    commit('SET_AI_AGENT_TARGET_WORK_ORDER', workOrderInfo);
    console.log('🤖 设置智能体消息目标工单:', workOrderInfo);
  },

  // 检查消息是否针对当前工单（保留用于其他地方调用）
  checkMessageTargetWorkOrder({ state }, messageWorkOrderInfo) {
    const currentWo = state.currentActiveWorkOrder;
    const messageWo = messageWorkOrderInfo || {};

    // 如果没有设置当前活跃工单，允许所有消息（向后兼容）
    if (!currentWo.woId && !currentWo.sheetNo) {
      return true;
    }

    // 如果消息没有指定目标工单，允许消息（向后兼容）
    if (!messageWo.woId && !messageWo.sheetNo) {
      return true;
    }

    // 检查工单ID或工单编号是否匹配
    const woIdMatch = currentWo.woId && messageWo.woId && currentWo.woId === messageWo.woId;
    const sheetNoMatch = currentWo.sheetNo && messageWo.sheetNo && currentWo.sheetNo === messageWo.sheetNo;

    const isMatch = woIdMatch || sheetNoMatch;

    console.log('🤖 Vuex消息目标工单检查:', {
      currentWorkOrder: currentWo,
      messageWorkOrder: messageWo,
      isMatch,
      woIdMatch,
      sheetNoMatch
    });

    return isMatch;
  },

  // 清除当前活跃工单
  clearCurrentActiveWorkOrder({ commit }) {
    commit('CLEAR_CURRENT_ACTIVE_WORK_ORDER');
    console.log('🤖 清除当前活跃工单');
  },

  // 发送消息给智能体
  sendMessageToAiAgent({ state }, messageData) {
    try {
      console.log('🤖 Vuex发送消息给智能体:', messageData);

      // 检查智能体是否可用
      if (!state.isAiAgentAvailable || !state.showAiAgent) {
        console.warn('🤖 智能体不可用或未显示，无法发送消息');
        return false;
      }

      // 通过全局智能体服务发送消息
      if (window.Vue && window.Vue.prototype.$globalAiAgent) {
        const success = window.Vue.prototype.$globalAiAgent.sendMessage(messageData);
        if (success) {
          console.log('🤖 通过Vuex成功发送消息给智能体:', messageData);
          return true;
        }
      }

      // 备用方案：通过postMessage发送
      const aiAgentIframe = document.querySelector('.ai-agent-iframe');
      if (aiAgentIframe && aiAgentIframe.contentWindow) {
        aiAgentIframe.contentWindow.postMessage(messageData, '*');
        console.log('🤖 通过iframe postMessage发送消息给智能体:', messageData);
        return true;
      }

      console.warn('🤖 无法找到智能体通信渠道');
      return false;
    } catch (error) {
      console.error('🤖 发送消息给智能体失败:', error);
      return false;
    }
  },

  // ========== 页面失活智能体状态管理 ==========
  // 页面失活时的智能体状态处理
  handlePageDeactivated({ commit, state }) {
    console.log('🤖 页面失活处理');

    // 如果启用了页面失活自动隐藏功能，且当前智能体是显示状态，则隐藏
    if (state.userPreferences.autoHideOnPageDeactivate && state.showAiAgent) {
      console.log('🤖 页面失活，自动隐藏智能体');
      commit('SET_SHOW_AI_AGENT', false);
    }
  },
};

const getters = {
  // 智能体是否可用
  isAiAgentAvailable: state => state.isAiAgentAvailable,

  // 是否显示智能体
  showAiAgent: state => state.showAiAgent,

  // 智能体加载状态
  aiAgentLoading: state => state.aiAgentLoading,

  // 智能体错误状态
  aiAgentError: state => state.aiAgentError,

  // 智能体URL
  aiAgentUrl: state => state.aiAgentUrl,

  // 智能体配置
  aiAgentConfig: state => state.aiAgentConfig,

  // 用户偏好
  userPreferences: state => state.userPreferences,

  // 当前路由是否支持智能体
  currentRouteSupported: state => {
    return state.isAiAgentAvailable && state.currentRoute;
  },

  // ========== 新增：多工单消息过滤相关getters ==========
  // 当前活跃工单信息
  currentActiveWorkOrder: state => state.currentActiveWorkOrder,

  // 智能体消息目标工单信息
  aiAgentTargetWorkOrder: state => state.aiAgentTargetWorkOrder,
};

// 辅助函数

// 检查页面是否支持智能体功能
async function checkPageSupport(route) {
  // 目前主要支持工单详情页面
  const supportedPages = [
    'common_orderDetail',
    'CommonWoDetail',
    // 可以添加更多支持的页面
  ];

  return supportedPages.includes(route.name) ||
         route.path.includes('orderDetail') ||
         route.path.includes('WoDetail');
}

// 检查专业类型是否支持
async function checkProfessionSupport(route) {
  try {
    // 专业类型支持检查现在由页面直接控制
    // 这里保持默认支持，具体的网络类型检查在页面中进行
    return true;
  } catch (error) {
    console.error('Failed to check profession support:', error);
    return false;
  }
}

// 加载智能体配置
async function loadAiAgentConfig(route) {
  try {
    // 使用配置文件中的智能体URL（支持环境自动切换）
    const mockResponse = await mockApiCall('getConfig');

    return {
      url: getAiAgentUrl(), // 使用配置文件中的URL
      config: mockResponse.data,
    };
  } catch (error) {
    console.error('Failed to load AI agent config:', error);
    throw error;
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
