<template>
  <div>
    <el-form
      ref="transferForm"
      :model="transferForm"
      :rules="transferFormRules"
      label-width="90px"
    >
      <el-form-item label="转派说明:">
        <el-input
          type="textarea"
          placeholder="请填写转派说明"
          v-model="transferForm.explain"
          style="width: 310px"
          show-word-limit
          maxlength="255"
        ></el-input>
        <div class="el-form-item__error" v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="转派对象:" prop="transferObject" required>
        <el-input
          readonly
          v-model="transferForm.transferObject"
          placeholder="添加人员"
          style="width: 310px"
        >
          <template v-for="(tag, index) in organizeForm.builderZsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              :closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              :closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderZsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderZsName"
              placeholder="请输入转派人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderZs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderZs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderZsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderZs', scope.row)"
                    >移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderZs')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 3px"
              >+{{ organizeForm.builderZsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('transferObjectDetermine')"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="抄送:" prop="cc">
        <el-input
          readonly
          v-model="transferForm.cc"
          placeholder="添加人员"
          style="width: 310px"
        >
          <template v-for="(tag, index) in organizeForm.builderCsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              :closable="true"
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              :closable="true"
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderCsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderCsName"
              placeholder="请输入抄送人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderCs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderCs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderCsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderCs', scope.row)"
                    >移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderCs')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 5px"
              >+{{ organizeForm.builderCsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('ccDetermine')"
          ></el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="transferSubmit('transferForm')"
        v-loading.fullscreen.lock="transferSubmitLoading"
        >提 交</el-button
      >
      <el-button @click="transferCancel">取 消</el-button>
    </div>
    <!--    <dia-orgs-user-tree-other-->
    <!--      :title="diaPeople.title"-->
    <!--      :visible.sync="diaPeople.visible"-->
    <!--      :showOrgsTree="diaPeople.showOrgsTree"-->
    <!--      :professionalType="common.professionalTypeName"-->
    <!--      @on-save="onSavePeople"-->
    <!--      :appendToBody="true"-->
    <!--    />-->
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :professionalType="common.professionalTypeName"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="diaPeople.showOrgsTree"
      :reassignmentType="reassignmentType"
    />
  </div>
</template>
<script>
import {
  apiRedeployUserTree,
  apiRedeployOrgTree,
} from "../../../workOrder/api/WorkOrderTodo.js";
import { apiActionPublic, getCurrentTime } from "../api/CommonApi";
// import DiaOrgsUserTreeOther from "./DiaOrgsUserTreeOther.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

export default {
  name: "TurnToSend",
  props: {
    common: Object,
    type: String,
    actionName: String,
  },
  components: {
    DiaTissueTree,
  },
  data() {
    return {
      showTip: false,
      showTime: 5000,
      reassignmentType: "province",
      isDiaOrgsUserTree: false,
      userAttribution: "cpTurnToSendUser",
      transferForm: {
        explain: "",
        transferObject: "",
        cc: "",
        transferObjectUserId: "",
        transferObjectOrgId: "",
        ccUserId: "",
        ccOrgId: "",
        agentMan: "",
        copyMan: "",
        agentDeptName: "",
        copyDeptName: "",
        agentManDetail: null,
        copyManDetail: null,
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      },
      multipleSelection: [], //多选人员
      transferFormRules: {
        transferObject: [{ required: true, message: "转派对象不能为空" }],
      },
      //抄送
      ccActiveName: "orgTree",
      ccDialogVisible: false,
      ccProRedepolyOrgsData: [],
      ccProRedeployUserData: [],
      //主送
      activeName: "orgTree",
      proRedepolyOrgsData: [],
      proRedeployUserData: [],
      transferObjectDialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      proOrgTreeLoading: false,
      proUserTreeLoading: false,
      woId: null,
      workItemId: null,
      transferSubmitLoading: false,
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          transferObjectDetermine: "选择转派人",
          ccDetermine: "选择抄送人",
        },
        showOrgsTree: true,
      },
    };
  },
  mounted() {},
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.transferForm.transferObject = "已选";
        } else {
          this.transferForm.transferObject = "";
        }
      },
      deep: true,
    },
    "organizeForm.builderCsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.transferForm.cc = "已选";
        } else {
          this.transferForm.cc = "";
        }
      },
      deep: true,
    },
  },
  methods: {
    descTip(count, name, showName) {
      if (this.transferForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      } else {
        this[showName] = false;
      }
    },

    // selectCc() {
    //   this.ccDialogVisible = true;
    // },
    //转派提交
    transferSubmit(formName) {
      this.entering();
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.transferSubmitLoading = true;
          // if (this.type == "batch") {
          //   let woIdArr = this.common.multipleSelections.map(item => {
          //     return item.woId;
          //   });
          //   let workItemIdArr = this.common.multipleSelections.map(item => {
          //     return item.workItemId;
          //   });
          //
          //   this.woId = woIdArr.join(",");
          //   this.workItemId = workItemIdArr.join(",");
          // } else if (this.type == "single") {
          //   this.woId = this.common.woId;
          //   this.workItemId = this.common.workItemId;
          // }

          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            processNode: this.common.processNode,
            actionName:
              this.common.processNode == "上传故障报告"
                ? "故障报告转派"
                : "转派",
            turnReason: this.transferForm.explain, //转派说明
            agentManId: this.transferForm.transferObjectUserId,
            agentMan: this.transferForm.agentManDetail,
            agentDeptCode: this.transferForm.transferObjectOrgId,
            agentDeptName: this.transferForm.agentDeptName,
            ccPersonId: this.transferForm.ccUserId,
            ccPerson: this.transferForm.copyManDetail,
            copyDeptCode: this.transferForm.ccOrgId,
            copyDeptName: this.transferForm.copyDeptName,
          };

          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.$emit("closeDialogTurnToSend", "1");
              } else if (res.status == "200") {
                this.$message.warning(res.msg);
                this.$emit("closeDialogTurnToSend", "0");
              } else {
                this.$message.error(res.msg);
              }
              this.transferSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.transferSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //转派取消
    transferCancel() {
      this.$emit("closeDialogTurnToSend", "0");
    },
    // transferObjectHandleClose() {
    //   this.transferObjectDialogVisible = false;
    //   this.resetTransferObjectChecked();
    // },
    // resetTransferObjectChecked() {
    //   this.$refs.transferObjectOrgTree.setCheckedKeys([]);
    //   this.$refs.transferObjectUserTree.setCheckedKeys([]);
    // },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    transferObjectDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transfer = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transfer > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let transferOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (transferOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }

      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    ccHandleClose() {
      this.ccDialogVisible = false;
    },
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transferCs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let transferCs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    // stitchingAlgorithmNew(orgName, userName) {
    //   if (orgName.length !== 0 && userName.length !== 0) {
    //     return orgName + "," + userName;
    //   } else {
    //     if (orgName.length !== 0) {
    //       return orgName;
    //     } else if (userName.length !== 0) {
    //       return userName;
    //     } else {
    //       return "";
    //     }
    //   }
    // },

    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
    },
    entering() {
      //先清空
      this.transferForm.agentMan = "";
      this.transferForm.transferObjectUserId = "";
      this.transferForm.agentManDetail = "";
      this.transferForm.agentDeptName = "";
      this.transferForm.transferObjectOrgId = "";
      this.transferForm.copyMan = "";
      this.transferForm.ccUserId = "";
      this.transferForm.copyManDetail = "";
      this.transferForm.copyDeptName = "";
      this.transferForm.ccOrgId = "";

      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.transferForm.agentMan = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.transferForm.transferObjectUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.transferForm.agentManDetail = userDetailName.join(",");
        }

        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.transferForm.agentDeptName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.transferForm.transferObjectOrgId = orgsCheckedId.join(",");
        }
      }

      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.transferForm.copyMan = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.transferForm.ccUserId = usersCheckedId.join(",");

          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.transferForm.copyManDetail = userDetailName.join(",");
        }

        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.transferForm.copyDeptName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.transferForm.ccOrgId = orgsCheckedId.join(",");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
