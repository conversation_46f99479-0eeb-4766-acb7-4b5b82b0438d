<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">告警详情</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getTableData()"
          >刷新</el-button
        >
        <el-button
          type="primary"
          size="mini"
          v-if="tableData.length > 0"
          @click="syncClearAlarm"
          v-loading.fullscreen.lock="syncClearAlarmFullscreenLoading"
          >同步清除告警</el-button
        >
        <el-button
          type="primary"
          size="mini"
          v-if="clearApplyBtnShow"
          @click="clearApply"
          v-loading.fullscreen.lock="syncClearAlarmFullscreenLoading"
          >告警清除申请</el-button
        >
        <el-button
          type="primary"
          size="mini"
          v-if="manualBtnShow"
          @click="manualBtnClick"
          v-loading.fullscreen.lock="syncClearAlarmFullscreenLoading"
          >人工确认清除</el-button
        >
      </div>
    </div>

    <div class="_el-table">
      <el-table
        :data="tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
        v-loading="tableLoading"
        @row-click="opentableList"
      >
        <el-table-column
          type="selection"
          :selectable="selectDisableRoom"
          width="55"
        >
        </el-table-column>
        <el-table-column
          prop="professionalTypeName"
          label="专业"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="locateNeName"
          label="告警对象"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="alarmTitle"
          label="标题"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="locateneClass"
          label="告警类型"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="alarmLocation"
          label="告警定位"
          width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="nmsAlarmSerial"
          label="告警编码"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="主告警"
          prop="isMajorAlarm"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="追加告警"
          prop="isAppendedAlarm"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="故障发生时间"
          prop="alarmCreateTime"
          width="140"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="添加时间"
          prop="loadTime"
          width="140"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="自动清除时间"
          prop="autoClearTime"
          width="140"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="人工清除时间"
          prop="manualClearTime"
          width="140"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="实际清除时间"
          prop="alarmClearTime"
          width="140"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
    <div
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <span
        >共计<span style="color: #b50b14">{{ form.total }}</span
        >条告警，其中追加告警<span style="color: #b50b14">{{
          form.addTotal
        }}</span
        >条。</span
      >
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        layout="->, total, sizes, prev, pager, next"
        @change="getTableData()"
      />
    </div>

    <el-dialog
      :visible.sync="tableListVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1300px"
      title="告警详情"
    >
      <div style="height: 500px; overflow-y: auto">
        <el-descriptions title="告警信息" class="margin-top" :column="3" border>
          <el-descriptions-item>
            <template slot="label"> 告警流水号 </template>
            <div class="tdOverflow" :title="tableListData.alarmId">
              {{ tableListData.alarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 原告警号 </template>
            <div class="tdOverflow" :title="tableListData.oriAlarmId">
              {{ tableListData.oriAlarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警对象 </template>
            <div class="tdOverflow" :title="tableListData.locateNeName">
              {{ tableListData.locateNeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label"> 告警标题 </template>
            <div class="tdOverflow" :title="tableListData.alarmTitle">
              {{ tableListData.alarmTitle }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 专业 </template>
            <div class="tdOverflow">
              {{ tableListData.professionalTypeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网络类型 </template>
            <div class="tdOverflow">
              {{ tableListData.networkType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 发生时间 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmCreateTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警地市 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmRegion }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警定位 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmLocation }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警级别 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmLevelName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警处理响应级别 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmHandleLevel }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业务是否中断 </template>
            <div class="tdOverflow">
              {{ tableListData.businessDown }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业务影响范围 </template>
            <div class="tdOverflow">
              {{ tableListData.effectRange }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 自动清除时间 </template>
            <div class="tdOverflow">
              {{ tableListData.autoClearTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 人工清除时间 </template>
            <div class="tdOverflow">
              {{ tableListData.manualClearTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 实际清除时间 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmClearTime }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 设备类型 </template>
            <div class="tdOverflow" :title="tableListData.neTypeName">
              {{ tableListData.neTypeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备厂家 </template>
            <div class="tdOverflow" :title="tableListData.alarmVendor">
              {{ tableListData.alarmVendor }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备型号 </template>
            <div class="tdOverflow" :title="tableListData.equipType">
              {{ tableListData.equipType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑分类 </template>
            <div class="tdOverflow" :title="tableListData.alarmType">
              {{ tableListData.alarmType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑子类 </template>
            <div class="tdOverflow" :title="tableListData.alarmSubType">
              {{ tableListData.alarmSubType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 资源系统网元唯一标识 </template>
            <div class="tdOverflow" :title="tableListData.neId">
              {{ tableListData.neId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警编码 </template>
            <div class="tdOverflow" :title="tableListData.nmsAlarmSerial">
              {{ tableListData.nmsAlarmSerial }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label"> 告警标准名 </template>
            <div class="tdOverflow" :title="tableListData.standardAlarmName">
              {{ tableListData.standardAlarmName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 远端对象类型 </template>
            <div class="tdOverflow" :title="tableListData.rLocateNeType">
              {{ tableListData.rLocateNeType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label"> 远端对象名称 </template>
            <div class="tdOverflow" :title="tableListData.rLocateNeName">
              {{ tableListData.rLocateNeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label"> 告警描述 </template>
            <div
              class="tdOverflow"
              :title="tableListData.alarmDetail"
              v-html="return2Br(tableListData.alarmDetail)"
            ></div>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          title="专业故障信息"
          style="margin-top: 16px"
          :column="3"
          border
        >
          <el-descriptions-item>
            <template slot="label"> 基站编码 </template>
            <div class="tdOverflow" :title="tableListData.bsCode">
              {{ tableListData.bsCode }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 基站名称 </template>
            <div class="tdOverflow" :title="tableListData.bsName">
              {{ tableListData.bsName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 基站等级 </template>
            <div class="tdOverflow" :title="tableListData.bsLevelName">
              {{ tableListData.bsLevelName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 基站地址 </template>
            <div class="tdOverflow" :title="tableListData.bsAddress">
              {{ tableListData.bsAddress }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业主电话 </template>
            <div class="tdOverflow" :title="tableListData.propertyPhone">
              {{ tableListData.propertyPhone }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 规模退服 </template>
            <div class="tdOverflow" :title="tableListData.scaleService">
              {{ tableListData.scaleService }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <el-dialog
      title="人工确认清除告警"
      :visible="confirmDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="confirmDialogVisible = false"
      width="550px"
    >
      <el-form ref="confirmForm" :model="confirmForm" :rules="rules">
        <el-form-item label="告警清除时间:" prop="manualAckClearTime" required>
          <el-date-picker
            v-model="confirmForm.manualAckClearTime"
            type="datetime"
            placeholder="请选择清除时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 60%"
          />
        </el-form-item>
        <el-form-item
          class="foolter_button"
          style="margin-bottom: 0; text-align: right"
        >
          <el-button type="primary" @click="manualClear('confirmForm')"
            >提交</el-button
          >
          <el-button @click="resetconfirmForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-card>
</template>

<script>
import Pagination from "../../../workOrder/components/Pagination.vue";
import {
  apiQueryAlarmDetail,
  apiSyncClear,
  apiAlarmClearApply,
  apiManualClear,
} from "../api/CommonApi";
import moment from "moment";

export default {
  name: "AlarmDetail",
  components: {
    Pagination,
  },
  props: {
    // woId: String,
    common: Object,
    clearApplyBtnShow: Boolean,
    manualBtnShow: Boolean,
  },
  data() {
    var validManualAckClearTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择时间"));
      } else {
        let seconds3 = moment(
          this.confirmForm.manualAckClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");

        var isWrong = false;
        this.multipleSelection.forEach(item => {
          let seconds4 = moment(
            this.confirmForm.manualAckClearTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(item.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (seconds4 < 0) {
            isWrong = true;
          }
        });

        if (seconds3 > 0) {
          callback(new Error("人工清除时间不能晚于当前时间，请重新填写。"));
        }

        if (isWrong) {
          callback(
            new Error("人工清除时间早于所选告警的发生时间，请重新填写。")
          );
        } else {
          callback();
        }
      }
    };
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择清除时间"));
      } else {
        let seconds = moment(
          this.confirmForm.manualAckClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(this.occurrenceTime, "YYYY-MM-DD HH:mm:ss"), "seconds");
        if (seconds < 0) {
          callback(new Error("“清除时间”不能早于“发生时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        addTotal: 0,
      },
      // selectDetailForm: {
      //   alarmTitle: "",
      //   isMajorAlarm: "",
      //   isAppendAlarm: "",
      // },
      //确认清除
      confirmForm: {
        manualAckClearTime: "",
      },
      rules: {
        // opinion: [{ required: true, message: "请选择意见", trigger: "change" }],
        manualAckClearTime: [
          {
            validator: validManualAckClearTime,
            required: true,
          },
        ],
      },
      tableData: [],
      tableListData: {},
      tableLoading: false,
      tableListVisible: false,
      multipleSelection: [],
      alarmStaIdArr: [],
      alarmCreateTime: "",
      syncClearAlarmFullscreenLoading: false,
      confirmDialogVisible: false,
    };
  },
  mounted() {
    this.confirmForm.manualAckClearTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getTableData();
  },
  methods: {
    getTableData() {
      // if (type == "refresh") {
      //   this.selectDetailForm.alarmTitle = "";
      //   this.selectDetailForm.isMajorAlarm = "";
      //   this.selectDetailForm.isAppendAlarm = "";
      // }
      this.tableLoading = true;
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        woId: this.common.woId,
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.info ?? [];
            this.tableListData = res?.data?.info ?? [];
            this.form.total = res?.data?.total ?? 0;
            this.form.addTotal = res?.data?.addTotal ?? 0;
            // this.alarmStaIdArr = this.tableData.map(item => {
            //   return item.alarmStaId;
            // });
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //打开详情框
    opentableList(row) {
      this.tableListVisible = true;
      this.tableListData = row;
    },

    onResetTurnSingle() {
      this.selectDetailForm = {
        ...this.$options.data,
      };
    },
    syncClearAlarm() {
      this.syncClearAlarmFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiSyncClear(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.getTableData();
          } else {
            this.$message.error(res.msg);
          }
          this.syncClearAlarmFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.syncClearAlarmFullscreenLoading = false;
        });
    },

    //告警清除申请
    clearApply() {
      this.syncClearAlarmFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
        actionName: "告警清除申请",
      };
      apiAlarmClearApply(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.getTableData();
          } else {
            this.$message.error(res.msg);
          }
          this.syncClearAlarmFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.syncClearAlarmFullscreenLoading = false;
        });
    },

    manualBtnClick() {
      if (this.multipleSelection.length <= 0) {
        this.$message.warning("请选择告警");
      } else {
        this.confirmDialogVisible = true;
      }
    },

    //人工确认清除
    manualClear(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.syncClearAlarmFullscreenLoading = true;
          let alarmIds = this.multipleSelection.map(item => {
            return item.alarmId;
          });
          let jsonParam = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            actionName: "告警清除",
            clearTime: this.confirmForm.manualAckClearTime,
            alarmId: alarmIds,
          };
          apiManualClear(jsonParam)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.confirmDialogVisible = false;
                this.getTableData();
                this.isShowManualConfirm = false;
              } else {
                this.$message.error(res.msg);
              }
              this.syncClearAlarmFullscreenLoading = false;
            })
            .catch(err => {
              console.log(err);
              this.syncClearAlarmFullscreenLoading = false;
              this.$message.error(err.msg);
            });
        } else {
          return false;
        }
      });
    },
    //重置
    resetconfirmForm() {
      this.confirmForm = {
        // opinion: "同意",
        manualAckClearTime: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
      };
    },

    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },

    //选择改变
    handleSelectionChange(val) {
      val.forEach(el => {
        delete el.data;
      });
      this.multipleSelection = val;
    },

    //列表项是否可选
    selectDisableRoom(row) {
      return row.manualClearTime == null || row.manualClearTime == "";
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 100%;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    /*white-space: nowrap;*/
    /*line-height: 50px;*/
    -ms-overflow-style: none;
    scrollbar-width: none;
    display: table-cell;
    vertical-align: middle;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
