<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">上传故障报告信息</span>

      <div class="header-right" v-show="isShowReportReview">
        <el-button type="primary" size="mini" @click="reportAudit"
        >故障报告审核</el-button
        >
      </div>
    </div>

    <div class="_el-table">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        border
        :tree-props="{children: 'sonInfo', hassonInfo: 'hassonInfo'}"
        @selection-change="handleSelectionChange"
        v-loading="tableLoading"
      >
        <el-table-column type="selection"
                         :selectable="selectDisableRoom"
                         width="55"> </el-table-column>
        <el-table-column
          prop="auditstatus"
          label="审核状态"
          width="140"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="uploadreporg"
          label="上报单位"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="uploadrepperson"
          label="上报人"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="createtime"
          label="上报时间"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="files"
          label="故障报告"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-tag
              v-for="(item, index) in scope.row.files"
              class="fileName_style"
              :key="index"
              @click="previewAppendixFile(item, index)"
              v-loading.fullscreen.lock="appendixFileLoading"
              :title="item.attOrigName"
            ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="detail"
          label="描述"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="审批结果"
          prop="auditresult"
          min-width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="审批意见"
          prop="auditopinion"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>

    <!--  故障报告0  -->
    <el-dialog
      title="故障报告审核"
      :visible.sync="dialogReportAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogReportAuditClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="3"
        actionName="故障报告审核"
        :multipleSelection="multipleSelection"
        @dialogReportAuditSubmitClose="dialogReportAuditSubmitClose"
      ></audit>
    </el-dialog>
    <!-- 使用图片预览组件 -->
    <image-preview
      :visible.sync="imagePreviewVisible"
      :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download"
      :use-custom-download="true"
      @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"
    ></image-preview>
  </el-card>
</template>

<script>
import {
  apiFileDownload,
  apiQueryUploadFileDetail,
  apiDownloadAppendixFile,
} from "../api/CommonApi";
// import { Audit } from "./Audit.vue";
import Audit from "./Audit";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  name: "UploadFaultFile",
  components: {
    Audit,
    ImagePreview,
  },
  props: {
    isShowReportReview: Boolean, //判断故障报告审核按钮
    common: Object,
  },
  data() {
    return {
      dialogReportAuditVisible: false,
      multipleSelection: [],
      tableData: [],
      tableLoading: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
      appendixFileLoading: false,
    };
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },

    getTableData() {

      this.tableLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiQueryUploadFileDetail(param)
        .then(res => {
          if (res.status == "0") {

            // res = {"status":"0","data":[{"auditstatus":"待审核","createtime":"2023-03-22 17:03:42","linkid":"20230322173742_7eJLMjQXL33eI5RYvX9","uploadrepperson":"root-bj","processinstid":2000840050,"auditresult":null,"sonInfo":[{"auditstatus":"待审核","createtime":"2023-03-22 17:03:32","linkid":"20230322171131_zXrHSD5jTuQjL69j0IY","uploadrepperson":"bjznjk","processinstid":2000840039,"auditresult":null,"files":[{"attId":"c3b9c364a7c14536a02caef626c699f9","attGroupKey":"20230322170741_mQiwSGWtdyy8SK6foke","attOrigName":"新建文本文档 (2).txt","attSuffix":".txt","createTime":"2023-03-22 17:11:30","createPCode":"zhangsan","createPName":"张三","sheetCreateTime":"2023-03-22 17:07:41","processNode":"上传故障报告","processId":"20230322171129_5ZqOcKtpjM5FMxdKvj4"}],"auditopinion":null,"detail":"121","uploadreporg":"北京市分公司","attatchgroupkey":"20230322171129_5ZqOcKtpjM5FMxdKvj4"},{"auditstatus":"待审核","createtime":"2023-03-22 17:03:06","linkid":"20230322173706_eHdq3qDEnRdt6r0jLoK","uploadrepperson":"zhjk_bj","processinstid":2000840051,"auditresult":null,"files":[{"attId":"5d68460c002049ca844bc43409c395d4","attGroupKey":"20230322170741_mQiwSGWtdyy8SK6foke","attOrigName":"新建文本文档 (2).txt","attSuffix":".txt","createTime":"2023-03-22 17:37:05","createPCode":"zhangsan","createPName":"张三","sheetCreateTime":"2023-03-22 17:07:41","processNode":"上传故障报告","processId":"20230322173705_QEEuphcljZBrBzdMmgl"}],"auditopinion":null,"detail":"555","uploadreporg":"北京市分公司","attatchgroupkey":"20230322173705_QEEuphcljZBrBzdMmgl"}],"files":[{"attId":"36e123faed9d43f1bde2e59c8e4c0ca1","attGroupKey":"20230322170741_mQiwSGWtdyy8SK6foke","attOrigName":"省内故障工单列表信息_20230217102120 (1).xlsx","attSuffix":".xlsx","createTime":"2023-03-22 17:37:41","createPCode":"zhangsan","createPName":"张三","sheetCreateTime":"2023-03-22 17:07:41","processNode":"上传故障报告","processId":"20230322173741_McjYeP80BXhT4EDR9CK"}],"auditopinion":null,"detail":"3333","uploadreporg":"北京市分公司","attatchgroupkey":"20230322173741_McjYeP80BXhT4EDR9CK"}],"msg":"查询工单上传故障报告信息详情成功"};
            let row = res?.data ?? [];

            if (row.length > 0) {
              row.forEach(el => {
                console.log(el);
                if (el.sonInfo?.length > 0) {
                  el.id = this.randomNumber();
                  el.sonInfo?.forEach(tl => {
                    tl.id = this.randomNumber();
                  });
                }
              });
            }
            this.tableData = row;
            this.tableLoading = false;
          }
          else {
            this.tableLoading = false;
          }
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    //显示审核界面
    reportAudit() {
      if (this.multipleSelection.length <= 0) {
        this.$message.warning("请选择需要审核的故障报告");
        return;
      }
      else
        this.dialogReportAuditVisible = true;
    },

    //选择改变
    handleSelectionChange(val) {
      val.forEach(el => {
        delete el.data;
      });
      this.multipleSelection = val;
    },

    //列表项是否可选
    selectDisableRoom(row) {
      return (row.sonInfo?true:false) && row.auditstatus != "已审核";
    },

    //审核关闭
    dialogReportAuditClose(){
      this.dialogReportAuditVisible = false;
    },

    //审核成功
    dialogReportAuditSubmitClose(){
      this.dialogReportAuditVisible = false;
      // this.getTableData();
      this.$emit("dialogReportAuditSubmitClose");
    },

    randomNumber() {
      let num = "";
      for (var i = 0; i < 4; i++) {
        num += Math.floor(Math.random() * 10);
      }
      return num;
    },
     // 预览附件文件
     previewAppendixFile(data, index) {
      debugger;
      let fileObj = data?.content?.[0]?.files?.[index] || data;

      // 首先检查文件名是否为图片类型
      const fileName = fileObj.attOrigName.toLowerCase();
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));
      fileObj.name = fileObj.attOrigName;
      fileObj.id = fileObj.attId;
      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(fileObj);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = fileObj;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },

  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 260px;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 50px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
