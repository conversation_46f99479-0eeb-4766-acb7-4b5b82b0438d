<template>
  <div class="back-single">
    <el-form ref="backSingleForm" :inline="false" class="demo-form-inline" :model="backSingleForm" label-width="130px"
      :rules="backSingleFormRule">
      <el-card shadow="never" header="故障定性信息" :body-style="{ padding: '20px 8px' }" class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType" :rules="{
              required: true,
              message: '请选择故障所属专业',
            }">
              <dict-select :value.sync="backSingleForm.professionalType" :dictId="60001" style="width: 100%"
                @change="changeProfessionalType" :woId="common.woId" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ backSingleForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ backSingleForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复时间:" prop="busRecoverTime">
              <el-date-picker v-model="backSingleForm.busRecoverTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerBusRecoverDuration"
                :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复历时:" required>
              {{ second2Time(backSingleForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <template v-if="!isCloudResourceFlow">
                <!-- 云资源流程（骨干云池、MEC）故障结束时间不可修改 -->
                <el-date-picker
                  v-if="!isCloudResourceFlow"
                  v-model="backSingleForm.faultEndTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  style="width: 100%"
                  @change="computerFaultTreatmentTime"
                  :picker-options="pickerOptions"
                  
                />
                <!-- 云资源流程显示固定时间 -->
                <el-input
                  v-else
                  v-model="backSingleForm.faultEndTime"
                  style="width: 100%"
                  placeholder="故障结束时间"
                />
              </template>
              <!-- isCloudResourceFlow -->
              <template v-else>
                {{ backSingleForm.faultEndTime }}
              </template>
              <!-- <el-date-picker v-model="backSingleForm.faultEndTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerFaultTreatmentTime"
                :picker-options="pickerOptions" /> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.backSingleForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生地区:" prop="faultRegion" :rules="{
              required: true,
              message: '请输入或者选择内容',
            }">
              <el-input v-model="backSingleForm.faultRegion" placeholder="请输入或者选择内容" clearable>
                <template #append>
                  <el-button type="primary" icon="el-icon-check" @click="faultRegionVisible = true"></el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ backSingleForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ backSingleForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.backSingleForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.backSingleForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group v-model="backSingleForm.isEffectBusiness" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="影响范围:" v-if="backSingleForm.isEffectBusiness == '1'" prop="effectRange">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="backSingleForm.effectRange"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="
          backSingleForm.professionalType != '25' &&
          backSingleForm.professionalType != '12' &&
          backSingleForm.professionalType != '13' &&
          backSingleForm.professionalType != '33' &&
          backSingleForm.professionalType != '5'
        ">
          <el-col :span="8">
            <el-form-item label="是否基站退服:" prop="isSiteOffline" :rules="{
              required: true,
              message: '请选择是否基站退服',
            }">
              <el-radio-group v-model="backSingleForm.isSiteOffline" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="退服原因:" v-if="backSingleForm.isSiteOffline == '1'" prop="siteOfflineReason" :rules="{
              required: backSingleForm.isSiteOffline == '1' ? true : false,
              message: '请选择退服原因',
            }">
              <dict-select :value.sync="backSingleForm.siteOfflineReason" :dictId="10016" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" header="故障专业信息" :body-style="{ padding: '20px' }" style="margin-top: 20px"
        class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障分类:" prop="faultCate" v-if="
              backSingleForm.professionalType != '0' &&
              backSingleForm.professionalType != '-1' &&
              backSingleForm.professionalType != '25' &&
              backSingleForm.professionalType != '30'
            " :rules="{
                required: true,
                message: '请选择故障分类',
              }">
              <dict-select :value.sync="backSingleForm.faultCate" :dictId="faultCateDict" style="width: 100%"
                placeholder="请选择内容" @change="changeFaultCate" :woId="common.woId" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障原因:" prop="faultReason" v-if="
              backSingleForm.professionalType != '0' &&
              backSingleForm.professionalType != '-1' &&
              backSingleForm.professionalType != '25' &&
              backSingleForm.professionalType != '30'
            " :rules="{
                required: true,
                message: '请选择故障原因',
              }">
              <dict-select :value.sync="backSingleForm.faultReason" :dictId="faultReasonDict" style="width: 100%"
                placeholder="请选择内容" :woId="common.woId" />
            </el-form-item>
          </el-col>
          <template v-if="
            backSingleForm.professionalType == '1' ||
            backSingleForm.professionalType == '31' ||
            backSingleForm.professionalType == '6' ||
            backSingleForm.professionalType == '19' ||
            backSingleForm.professionalType == '20' ||
            backSingleForm.professionalType == '21' ||
            backSingleForm.professionalType == '32' ||
            backSingleForm.professionalType == '28'
          ">
            <el-col :span="8">
              <el-form-item label="网元名称:" prop="neName">
                <el-input v-model="backSingleForm.neName" style="width: 100%" maxlength="255">
                </el-input>
              </el-form-item>
            </el-col>
          </template>

          <template>
            <el-col :span="8" v-if="
              backSingleForm.professionalType != '0' &&
              backSingleForm.professionalType != '-1' &&
              backSingleForm.professionalType != '25' &&
              backSingleForm.professionalType != '30' &&
              backSingleForm.professionalType != '1' &&
              backSingleForm.professionalType != '31' &&
              backSingleForm.professionalType != '6' &&
              backSingleForm.professionalType != '19' &&
              backSingleForm.professionalType != '20' &&
              backSingleForm.professionalType != '21' &&
              backSingleForm.professionalType != '28' &&
              backSingleForm.professionalType != '32' &&
              backSingleForm.professionalType != '5' &&
              backSingleForm.professionalType != '33' &&
              backSingleForm.professionalType != '12' &&
              backSingleForm.professionalType != '13'
            ">
              <el-form-item label="设备类型:" prop="eqpType" :rules="{
                required: true,
                message: '请选择设备类型',
              }">
                <dict-select :value.sync="backSingleForm.eqpType" :dictId="diviceTypeDict" style="width: 100%"
                  placeholder="请选择内容" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="
            backSingleForm.professionalType == '5' ||
            backSingleForm.professionalType == '33' ||
            backSingleForm.professionalType == '12' ||
            backSingleForm.professionalType == '13'
          ">
            <el-col :span="8">
              <el-form-item label="设备类型:">
                <dict-select :value.sync="backSingleForm.eqpType" :dictId="diviceTypeDict" style="width: 100%"
                  placeholder="请选择内容" />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8" v-if="
            backSingleForm.professionalType != '0' &&
            backSingleForm.professionalType != '-1' &&
            backSingleForm.professionalType != '25' &&
            backSingleForm.professionalType != '30' &&
            backSingleForm.professionalType != '1' &&
            backSingleForm.professionalType != '31' &&
            backSingleForm.professionalType != '6' &&
            backSingleForm.professionalType != '19' &&
            backSingleForm.professionalType != '20' &&
            backSingleForm.professionalType != '21' &&
            backSingleForm.professionalType != '32' &&
            backSingleForm.professionalType != '28'
          ">
            <el-form-item label="设备名称:" prop="eqpName">
              <el-input placeholder="请输入内容" v-model="backSingleForm.eqpName" style="width: 100%" maxlength="255">
                <el-button style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  " slot="append" @click="deviceSelect">选择</el-button>
              </el-input>
              <form id="sub__device" name="sub__device" hidden="true" method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment" target="_blank">
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="backSingleForm.professionalType == '26'">
            <el-form-item label="紧急程度:" prop="emergencyLevel">
              <el-input v-model="backSingleForm.emergencyLevel" :disabled="true" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="backSingleForm.professionalType == '26'">
            <el-form-item label="MEC节点名称:" prop="mecNodeName">
              <el-input v-model="backSingleForm.mecNodeName" style="width: 100%" maxlength="100">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="backSingleForm.professionalType == '26'">
            <el-form-item label="是否硬件故障:" prop="isHardFault" :rules="{
              required: true,
              message: '请选择是否硬件故障',
            }">
              <el-radio-group v-model="backSingleForm.isHardFault" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="相关附件:" prop="attachmentName">
              <el-tag class="fileName_style_download" closable v-for="(item, index) in fddxFileArr" :key="index"
                @close="closeAndDeleteFile(item)" @click="downloadAppendixFile(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag class="fileName_style" closable v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index" @close="close(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse">+上传附件</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否派单准确:" v-if="backSingleForm.professionalType == '7'" prop="isSentAccurately" :rules="{
              required: true,
              message: '请选择是否派单准确',
            }">
              <dict-select :value.sync="backSingleForm.isSentAccurately" :dictId="60006" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障厂家:" prop="vendor" v-if="
              backSingleForm.professionalType == '2' ||
              backSingleForm.professionalType == '4' ||
              backSingleForm.professionalType == '7' ||
              backSingleForm.professionalType == '10' ||
              backSingleForm.professionalType == '11'
            " :rules="{
                required: true,
                message: '请选择故障厂家',
              }">
              <el-radio-group v-model="backSingleForm.vendor">
                <el-radio v-for="(item, i) in vendorOptions" :key="i" :label="item.dictCode">{{ item.dictName
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="2"
                v-model="backSingleForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="falutComment">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="backSingleForm.falutComment"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" v-if="!sumBtnShow" @click="handleSubmit('backSingleForm')">提交</el-button>
      <el-button type="primary" v-else-if="
        sumBtnShow &&
        (backSingleForm.professionalType == '1' ||
          backSingleForm.professionalType == '31' ||
          backSingleForm.professionalType == '5' ||
          backSingleForm.professionalType == '33' ||
          backSingleForm.professionalType == '6' ||
          backSingleForm.professionalType == '12' ||
          backSingleForm.professionalType == '13' ||
          backSingleForm.professionalType == '19' ||
          backSingleForm.professionalType == '20' ||
          backSingleForm.professionalType == '21' ||
          backSingleForm.professionalType == '25' ||
          backSingleForm.professionalType == '32' ||
          backSingleForm.professionalType == '28')
      " @click="nextStepRule()">下一步</el-button>
      <el-button type="primary" v-else @click="nextStepEvaluation()">下一步</el-button>

      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog width="420px" title="附件选择" :visible.sync="relatedFilesDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-upload @change="changeFileData" @cancel="closeAttachmentDialog"></file-upload>
    </el-dialog>
    <el-dialog width="620px" title="返单审核规则" :visible.sync="ruleVisible" :close-on-click-modal="false" append-to-body>
      <p style="
          font-size: 16px;
          line-height: 130%;
          margin-top: 0;
          margin-bottom: 0;
        ">
        返单界面标记“*”符号字段必须按实际情况填写，审核时重点关注内容如下，填写信息包含即可，返单页面无对应字段的请自行选择字段填写：<br />
        (1)故障排查对象（具体系统、网元、设备）<br />
        (2)排查结果<br />
        (3)故障影响范围（据实反馈故障影响业务情况）<br />
        (4)故障处理情况<br />
        (5)故障原因分析<br />
        (6)反馈人信息(联系方式)。<br />
      </p>
      <span slot="footer" style="margin-top: 0">
        <el-button @click="ruleVisible = false">返回修改</el-button>
        <el-button type="primary" @click="nextStepEvaluationRule()">下一步</el-button>
      </span>
    </el-dialog>
    <el-dialog width="620px" title="满意度评价" :visible.sync="evaluationDialogVisible" :close-on-click-modal="false"
      :destroy-on-close="true" append-to-body>
      <el-form ref="evaluation" :model="evaluation" :rules="evaluationRules">
        <el-form-item label="派单准确度:" label-width="90px">
          <el-rate v-model="evaluation.orderAccuracy" :colors="colors"
            style="display: inline-block; vertical-align: text-bottom">
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确度:" label-width="90px">
          <el-rate v-model="evaluation.diagnosticrAccuracy" :colors="colors"
            style="display: inline-block; vertical-align: text-bottom">
          </el-rate>
        </el-form-item>
        <el-form-item v-if="
          evaluation.orderAccuracy <= 3 || evaluation.diagnosticrAccuracy <= 3
        " label="反馈问题:" label-width="90px" prop="feedbackProblemCheckList" :rules="[
            {
              required:
                evaluation.orderAccuracy > 3 &&
                  evaluation.diagnosticrAccuracy > 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]">
          <el-checkbox-group v-model="evaluation.feedbackProblemCheckList" @change="feedbackChange">
            <el-checkbox label="派单超时"></el-checkbox>
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="
          viewsOnContentShow &&
          (evaluation.orderAccuracy <= 3 ||
            evaluation.diagnosticrAccuracy <= 3)
        " prop="viewsOnContent">
          <el-input type="textarea" :rows="3" placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent" show-word-limit maxlength="255">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="backSingleFullscreenLoading">提 交</el-button>
        <el-button v-if="
          backSingleForm.professionalType == '1' ||
          backSingleForm.professionalType == '31' ||
          backSingleForm.professionalType == '5' ||
          backSingleForm.professionalType == '33' ||
          backSingleForm.professionalType == '6' ||
          backSingleForm.professionalType == '12' ||
          backSingleForm.professionalType == '13' ||
          backSingleForm.professionalType == '19' ||
          backSingleForm.professionalType == '20' ||
          backSingleForm.professionalType == '21' ||
          backSingleForm.professionalType == '25' ||
          backSingleForm.professionalType == '32' ||
          backSingleForm.professionalType == '28'
        " @click="nextStepEvaluationRule()">上一步</el-button>
        <el-button v-else @click="evaluationDialogVisible = false">上一步</el-button>
      </span>
    </el-dialog>

    <!-- 行政区划 -->
    <area-tree :visible.sync="faultRegionVisible" v-model="backSingleForm.faultRegion"></area-tree>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../workOrder/components/DictSelect.vue";
import FileUpload from "../../workOrder/components/FileUpload.vue";
import AreaTree from "@/plugin/backbone/components/AreaTree/AreaTree.vue";
import { apiQueryAlarmDetail } from "@plugin/backbone/modules/workOrder/workOrderWaitDetail/api/CommonApi";
import {
  apiGetFaultArea,
  apiBackSingle,
  apiGetEvaluation,
} from "../api/BackSingle";
import { apiQualitativeDetail } from "../api/QualitativeReview";
import {
  apiGetOrgInfo,
  apiDict,
  apiIsHaveAuth,
} from "../../workOrder/api/CommonApi";
import {
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import { mixin } from "../../../../../mixins";
export default {
  name: "BackSingle",
  props: {
    basicWorkOrderData: Object,
    common: Object,
    timing: Object,
  },
  components: { DictSelect, FileUpload, AreaTree },
  computed: {
    ...mapGetters(["userInfo"]),
    // 判断是否为云资源流程（集团通用 + 骨干云池/MEC）
    isCloudResourceFlow() {
      const professionalType = this.basicWorkOrderData?.professionalType;
      const networkTypeTop = this.common?.networkTypeTop;
      console.log('professionalType', professionalType, this.common);
      // 必须同时满足：1. 集团通用流程(networkTypeTop=5) 2. 专业类型为骨干云池或MEC
      return networkTypeTop === 5 && (professionalType === '骨干云池' || professionalType === 'MEC');
    },
  },
  mixins: [mixin],
  data() {
    var validBusRecoverTime = (rule, value, callback) => {
      if (this.backSingleForm.busRecoverTime) {
        // let seconds3 = moment(
        //   this.backSingleForm.busRecoverTime,
        //   "YYYY-MM-DD HH:mm:ss"
        // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        // let seconds4 = moment(
        //   this.backSingleForm.busRecoverTime,
        //   "YYYY-MM-DD HH:mm:ss"
        // ).diff(
        //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
        //   "seconds"
        // );
        // if (seconds3 > 0 || seconds4 <= 0) {
        //   callback(
        //     new Error(
        //       "当前时间>=业务恢复时间>=故障发生时间，请重新检查后选择正确时间"
        //     )
        //   );
        // } else {
        //   callback();
        // }
        let secondsBus = moment(
          this.backSingleForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (secondsBus <= 0) {
          callback(new Error("业务恢复历时<=0，请重新检查后选择正确时间！"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请选择业务恢复时间"));
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      // 云资源流程不需要验证故障结束时间（自动设置）
      if (this.isCloudResourceFlow) {
        callback();
        return;
      }
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.backSingleForm.faultEndTime) {
          // let seconds3 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          // let seconds4 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(
          //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          //   "seconds"
          // );
          let secondsProcessingDuration = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          let secondsProcessingDurationJing =
            secondsProcessingDuration - this.backSingleForm.suspendDuration;
          if (secondsProcessingDuration <= 0) {
            callback(new Error("故障处理历时<=0，请重新检查后选择正确时间！"));
          } else if (secondsProcessingDurationJing <= 0) {
            callback(
              new Error("故障处理净历时<=0，请重新检查后选择正确时间！")
            );
          } else {
            callback();
          }
        } else {
          callback(new Error("请选择故障结束时间"));
        }
      }
    };
    return {
      sumBtnShow: true,
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //业务恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: "",
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        //故障专业信息
        faultCate: null,
        faultReason: null,
        opticFiber: null,
        relatedFiles: null,
        effectSystem: null,
        falutReasonDesc: this.common.faultCauseDescription || "",
        falutComment: null,
        eqpType: null,
        eqpName: "",
        actionName: "",
        attachmentName: null,
        busName: null, //业务名称
        isHardFault: null, //是否硬件故障
        linkId: null,
        appendix: null,
        isSiteOffline: null,
        siteOfflineReason: null,
        isSentAccurately: "0",
        vendor: null,
        emergencyLevel: null,
        mecNodeName: null,
        neName: null, //网元名称
      },
      vendorOptions: [],
      faultCateDict: 60100,
      faultReasonDict: 0,
      diviceTypeDict: 0,
      areaCode: null, //区域编码
      category: null, //省份返单 OR 地市返单
      backSingleFullscreenLoading: false,
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      evaluationDialogVisible: false,
      evaluation: {
        orderAccuracy: 5,
        diagnosticrAccuracy: 5,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      evaluationRules: {
        viewsOnContent: [
          {
            required: true,
            message: "请填写内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "evaluation",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      faultRegionOptions: [],
      userData: {},
      // 云资源流程故障结束时间定时器
      faultEndTimeTimer: null,

      backSingleFormRule: {
        busRecoverTime: [{ validator: validBusRecoverTime, required: true }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
        neName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutReasonDesc: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 500,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        eqpName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        mecNodeName: [
          {
            validator: this.checkLength,
            max: 100,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectRange: [
          {
            required: true,
            message: "请填写影响范围",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      viewsOnContentShow: false,
      faultReasonOption: [],
      fddxFileArr: [],
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      ruleVisible: false,
      faultRegionVisible: false,
    };
  },
  watch: {
    "backSingleForm.busRecoverTime": {
      handler(val) {
        if (val == null) return;
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
    "backSingleForm.faultEndTime": {
      handler(val) {
        if (val == null) return;
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
  },
  created() {
    this.getEvaluation();
  },
  mounted() {
    // console.log(this.common);
    // console.log(this.basicWorkOrderData);
    this.backSingleForm.alarmCreateTime = this.common.failureTime;
    this.backSingleForm.sheetCreateTime = this.common.failureInformTime;
    this.backSingleForm.person = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    this.backSingleForm.processDefId = this.common.processDefId;
    this.backSingleForm.actionName = this.common.actionName;
    this.backSingleForm.professionalType = this.common.professionalType + "";
    this.backSingleForm.suspendDuration = this.common.hangOver;
    this.backSingleForm.emergencyLevel = this.common.emergencyLevel;
    this.backSingleForm.mecNodeName = this.common.mecNodeName;
    this.backSingleForm.busName = this.common.busName;
    this.backSingleForm.neName = this.basicWorkOrderData.neName;
    if (this.common.alarmClearTime) {
      this.backSingleForm.busRecoverTime = this.common.alarmClearTime;
      this.backSingleForm.faultEndTime = this.common.alarmClearTime;
      this.computerBusRecoverDuration();
      this.computerFaultTreatmentTime();
    }
    this.changeProfessionalType("init");
    this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    if (this.common.auditResult == "0") {
      this.qualitativeReviewDetail();
    }

    this.resourceBackInit(this.backSingleForm);
    let array = this.backSingleForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
     // 云资源流程初始化故障结束时间（首次加载时）
    if (this.isCloudResourceFlow) {
      this.initCloudResourceFaultEndTime();
    }
    this.getFaultRegionDef();
  },
  beforeDestroy() {
    // 清除云资源流程故障结束时间定时器
    if (this.faultEndTimeTimer) {
      clearInterval(this.faultEndTimeTimer);
      this.faultEndTimeTimer = null;
    }
  },
  methods: {
    changeProfessionalType(type) {
      if (type != "init") {
        this.backSingleForm.faultCate = "";
        this.backSingleForm.eqpType = "";
      }
      this.getVendorOptions(type);
      if (
        this.backSingleForm.professionalType == "1" ||
        this.backSingleForm.professionalType == "31" ||
        this.backSingleForm.professionalType == "6" ||
        this.backSingleForm.professionalType == "19" ||
        this.backSingleForm.professionalType == "20" ||
        this.backSingleForm.professionalType == "21" ||
        this.backSingleForm.professionalType == "32" ||
        this.backSingleForm.professionalType == "28"
      ) {
        this.faultCateDict = "60100";
      } else if (
        this.backSingleForm.professionalType == "2" ||
        this.backSingleForm.professionalType == "5" ||
        this.backSingleForm.professionalType == "33" ||
        this.backSingleForm.professionalType == "10" ||
        this.backSingleForm.professionalType == "11" ||
        this.backSingleForm.professionalType == "12" ||
        this.backSingleForm.professionalType == "13"
      ) {
        this.faultCateDict = "60101";
      } else if (this.backSingleForm.professionalType == "4") {
        this.faultCateDict = "60102";
      } else if (this.backSingleForm.professionalType == "7") {
        this.faultCateDict = "60103";
      } else if (this.backSingleForm.professionalType == "26") {
        this.faultCateDict = "60134";
      }
      if (
        this.backSingleForm.professionalType == "1" ||
        this.backSingleForm.professionalType == "31" ||
        this.backSingleForm.professionalType == "32" ||
        this.backSingleForm.professionalType == "28"
      ) {
        this.diviceTypeDict = "60127";
      } else if (this.backSingleForm.professionalType == "6") {
        this.diviceTypeDict = "60128";
      } else if (
        this.backSingleForm.professionalType == "2" ||
        this.backSingleForm.professionalType == "4" ||
        this.backSingleForm.professionalType == "5" ||
        this.backSingleForm.professionalType == "33" ||
        this.backSingleForm.professionalType == "10" ||
        this.backSingleForm.professionalType == "11" ||
        this.backSingleForm.professionalType == "12" ||
        this.backSingleForm.professionalType == "13"
      ) {
        this.diviceTypeDict = "60129";
      } else if (this.backSingleForm.professionalType == "7") {
        this.diviceTypeDict = "60130";
      } else if (
        this.backSingleForm.professionalType == "20" ||
        this.backSingleForm.professionalType == "21"
      ) {
        this.diviceTypeDict = "60131";
      } else if (this.backSingleForm.professionalType == "19") {
        this.diviceTypeDict = "60132";
      } else if (this.backSingleForm.professionalType == "26") {
        this.diviceTypeDict = "60136";
      }

      this.changeFaultCate(type);
      this.$emit(
        "professionalTypeChange",
        this.backSingleForm.professionalType
      );
    },
    getVendorOptions(type) {
      if (type != "init") {
        this.backSingleForm.vendor = null;
      }
      let param = {
        dictTypeCode: "60124",
      };
      if (
        this.backSingleForm.professionalType == "2" ||
        this.backSingleForm.professionalType == "10" ||
        this.backSingleForm.professionalType == "11"
      ) {
        this.$set(param, "dictTypeCode", "60124");
      } else if (this.backSingleForm.professionalType == "4") {
        this.$set(param, "dictTypeCode", "60125");
      } else if (this.backSingleForm.professionalType == "7") {
        this.$set(param, "dictTypeCode", "60126");
      }
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.vendorOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 处理弹窗打开事件（由父组件调用）
    handleDialogOpen() {
      if (this.isCloudResourceFlow) {
        this.initCloudResourceFaultEndTime();
      }
    },

    // 初始化云资源流程故障结束时间
    initCloudResourceFaultEndTime() {
      // 清除之前的定时器（如果存在）
      if (this.faultEndTimeTimer) {
        clearInterval(this.faultEndTimeTimer);
        this.faultEndTimeTimer = null;
      }

      // 立即设置故障结束时间为当前时间（返单时间）
      this.setCurrentFaultEndTime();

      // 启动定时器，每2分钟刷新一次
      this.faultEndTimeTimer = setInterval(() => {
        this.setCurrentFaultEndTime();
      }, 2 * 60 * 1000); // 2分钟 = 120000毫秒
    },

    // 设置当前故障结束时间并计算历时
    setCurrentFaultEndTime() {
      const currentTime = moment().format("YYYY-MM-DD HH:mm:ss");
      this.backSingleForm.faultEndTime = currentTime;

      // 立即计算故障处理历时
      this.computerFaultTreatmentTime();

      // 强制更新视图
      this.$forceUpdate();
    },
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    //查询是否已评价
    getEvaluation() {
      let param = {
        woId: this.common.woId,
      };
      apiGetEvaluation(param)
        .then(res => {
          if (res.status == "0") {
            let show = res?.data?.rows[0].isHaveEvaluation;

            if (show == "0") {
              this.sumBtnShow = true;
            } else {
              this.sumBtnShow = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            // this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.areaCode,
        category: this.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
            this.backSingleForm.faultRegion = this.faultRegionOptions[0].name;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeReviewDetail() {
      let param = {
        workItemId: this.backSingleForm.workItemId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.backSingleForm = res?.data?.rows?.[0] ?? {};
            this.changeFaultCate("init").then(() => {
              self.backSingleForm.faultReason =
                res?.data?.rows?.[0]?.faultReason ?? "";
            });
            if (res.data.rows[0].appendix) {
              self.fddxFileArr = JSON.parse(res.data.rows[0].appendix);
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.backSingleForm.relatedFiles = data.fileName;
      this.backSingleForm.attachmentName = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerBusRecoverDuration() {
      if (this.backSingleForm.busRecoverTime) {
        let days = moment(
          this.backSingleForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.busRecoverDuration = days;
      } else {
        this.backSingleForm.busRecoverDuration = 0;
      }
    },
    second2Time(days) {
      //return this.showTime(Math.abs(days));
      return this.showTime(days);
    },
    computerFaultTreatmentTime() {
      // 云资源流程使用故障通知时间，其他流程使用故障发生时间
      const startTime = this.isCloudResourceFlow
        ? this.backSingleForm.sheetCreateTime
        : this.backSingleForm.alarmCreateTime;

      let days = moment(
        this.backSingleForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(startTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-开始时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.backSingleForm.suspendDuration == 0) {
        this.backSingleForm.processDuration = this.backSingleForm.faultDuration;
      } else {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(startTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.processDuration =
          seconds - this.backSingleForm.suspendDuration;
      }
    },
    //挂起历时
    computerSuspendDuration() {
      if (this.timing.hangTime != "" && null != this.timing.hangTime) {
        if (
          this.timing.liftHangTime != "" &&
          null != this.timing.liftHangTime
        ) {
          let seconds = moment(
            this.timing.liftHangTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.backSingleForm.suspendDuration = seconds;
        } else {
          this.backSingleForm.suspendDuration = 0;
        }
      } else {
        this.backSingleForm.suspendDuration = 0;
      }
    },
    changeFaultCate(type) {
      if (type != "init") {
        this.backSingleForm.faultReason = "";
      }
      if (
        (this.backSingleForm.professionalType == "1" ||
          this.backSingleForm.professionalType == "31" ||
          this.backSingleForm.professionalType == "28" ||
          this.backSingleForm.professionalType == "32" ||
          this.backSingleForm.professionalType == "19") &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60104";
      } else if (
        (this.backSingleForm.professionalType == "1" ||
          this.backSingleForm.professionalType == "31" ||
          this.backSingleForm.professionalType == "28" ||
          this.backSingleForm.professionalType == "32" ||
          this.backSingleForm.professionalType == "19") &&
        this.backSingleForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60105";
      } else if (
        (this.backSingleForm.professionalType == "1" ||
          this.backSingleForm.professionalType == "31" ||
          this.backSingleForm.professionalType == "28" ||
          this.backSingleForm.professionalType == "32" ||
          this.backSingleForm.professionalType == "19") &&
        this.backSingleForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60106";
      } else if (
        this.backSingleForm.professionalType == "6" &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60108";
      } else if (
        this.backSingleForm.professionalType == "6" &&
        this.backSingleForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60109";
      } else if (
        this.backSingleForm.professionalType == "6" &&
        this.backSingleForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60110";
      } else if (
        (this.backSingleForm.professionalType == "5" ||
          this.backSingleForm.professionalType == "33" ||
          this.backSingleForm.professionalType == "11") &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60111";
      } else if (
        (this.backSingleForm.professionalType == "2" ||
          this.backSingleForm.professionalType == "5" ||
          this.backSingleForm.professionalType == "33" ||
          this.backSingleForm.professionalType == "10" ||
          this.backSingleForm.professionalType == "11" ||
          this.backSingleForm.professionalType == "12" ||
          this.backSingleForm.professionalType == "13") &&
        this.backSingleForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60112";
      } else if (
        (this.backSingleForm.professionalType == "5" ||
          this.backSingleForm.professionalType == "33" ||
          this.backSingleForm.professionalType == "10" ||
          this.backSingleForm.professionalType == "11" ||
          this.backSingleForm.professionalType == "12" ||
          this.backSingleForm.professionalType == "13") &&
        this.backSingleForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60113";
      } else if (
        this.backSingleForm.professionalType == "2" &&
        this.backSingleForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60114";
      } else if (
        (this.backSingleForm.professionalType == "10" ||
          this.backSingleForm.professionalType == "12" ||
          this.backSingleForm.professionalType == "13") &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60115";
      } else if (
        this.backSingleForm.professionalType == "2" &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60116";
      } else if (
        this.backSingleForm.professionalType == "20" ||
        this.backSingleForm.professionalType == "21"
      ) {
        this.faultReasonDict = "60117";
      } else if (
        this.backSingleForm.professionalType == "7" &&
        this.backSingleForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60118";
      } else if (
        this.backSingleForm.professionalType == "7" &&
        this.backSingleForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60119";
      } else if (
        this.backSingleForm.professionalType == "7" &&
        this.backSingleForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60120";
      } else if (
        this.backSingleForm.professionalType == "7" &&
        (this.backSingleForm.faultCate == "4" ||
          this.backSingleForm.faultCate == "5")
      ) {
        this.faultReasonDict = "60121";
      } else if (
        this.backSingleForm.professionalType == "4" &&
        (this.backSingleForm.faultCate == "1" ||
          this.backSingleForm.faultCate == "3")
      ) {
        this.faultReasonDict = "60122";
      } else if (
        this.backSingleForm.professionalType == "4" &&
        this.backSingleForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60123";
      } else if (this.backSingleForm.professionalType == "26") {
        this.faultReasonDict = "60135";
      } else if (
        null != this.backSingleForm.faultCate &&
        this.backSingleForm.faultCate != ""
      ) {
        this.faultReasonDict = "60107";
      }
    },
    //下一步评价
    nextStepEvaluation() {
      this.$refs.backSingleForm.validate((valid, a) => {
        if (this.uncheck(a)) {
          this.evaluationDialogVisible = true;
        } else {
          return false;
        }
      });
    },
    //下一步评价
    nextStepEvaluationRule() {
      this.ruleVisible = !this.ruleVisible;
      this.evaluationDialogVisible = !this.evaluationDialogVisible;
    },
    //下一步浏览规则
    nextStepRule() {
      this.$refs.backSingleForm.validate((valid, a) => {
        if (this.uncheck(a)) {
          this.ruleVisible = true;
        } else {
          return false;
        }
      });
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          // 当前剩余受理时限
          let remainAcceptTime = '-';
          if(!this.common.processTimeLimit || this.common.processTimeLimit == '' || this.common.processTimeLimit == '无' || this.common.processTimeLimit == '-'){

          }else{
            remainAcceptTime = this.common.processTimeLimit*60 - Math.floor((new Date() - new Date(this.backSingleForm.sheetCreateTime)) / 60000);
            if (remainAcceptTime <= 0) {
              remainAcceptTime = -1;
            }
          }
          this.backSingleForm.remainAcceptTime = remainAcceptTime;
          formData.append("jsonParam", JSON.stringify(this.backSingleForm));
          let evaluateParam = {
            woId: this.common.woId,
            sheetCreateTime: this.backSingleForm.sheetCreateTime,
            sendAccuracy: this.evaluation.orderAccuracy,
            diagnoseAccuracy: this.evaluation.diagnosticrAccuracy,
            evaluateContent: this.evaluation.viewsOnContent,
            problemClass:
              this.evaluation.feedbackProblemCheckList.length > 0
                ? this.evaluation.feedbackProblemCheckList.join(",")
                : "",
            remainAcceptTime: remainAcceptTime,
          };
          formData.append("evaluateParam", JSON.stringify(evaluateParam));
          apiBackSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交返单成功");
                this.evaluationDialogVisible = false;
                this.getIsHaveAuth(res.data);
                // this.$emit("closeBackSingleDialog", res.data);
              } else {
                this.$message.error("提交返单失败");
                this.backSingleFullscreenLoading = false;
              }
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交返单失败");
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },

    getIsHaveAuth(data) {
      //是否刷新当前页面
      let params = {
        userName: this.userInfo.userName,
        processInstId: data.processInstId,
      };
      apiIsHaveAuth(params)
        .then(res => {
          if (res.status == "0") {
            data.currentPage = res.data;
            this.$emit("closeBackSingleDialog", data);
            this.backSingleFullscreenLoading = false;
          } else {
            this.$message.error("获取是否刷新当前页面数据失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("获取是否刷新当前页面数据失败");
        });
    },
    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (this.importForm.relatedFilesFileList.length == 0) {
        this.backSingleForm.attachmentName = null;
      }
    },
    onReset() {
      this.backSingleForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        actionName: this.common.actionName,
        professionalType: this.common.professionalType + "",
        emergencyLevel: this.common.emergencyLevel,
        mecNodeName: this.common.mecNodeName,
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        effectSystem: null,
        faultReason: null,
        opticFiber: null,
        falutReasonDesc: this.common.faultCauseDescription || "",
        falutComment: null,
        eqpType: null,
        eqpName: null,
        faultEndTime: this.common.alarmClearTime,
        busName: null,
        isHardFault: null,
        faultCate: null,
        busRecoverTime: this.common.alarmClearTime, //业务恢复时间
        busRecoverDuration: 0,
        faultRegion: "",
        vendor: null,
        isSentAccurately: "0",
      };
      this.vendorOptions = [];
      this.faultCateDict = 60100;
      this.faultReasonDict = 0;
      this.diviceTypeDict = 0;
      this.importForm.relatedFilesFileList = [];
      this.changeProfessionalType();
      this.computerBusRecoverDuration();
      this.computerFaultTreatmentTime();
    },
    showTime(val) {
      let isPositiveNumber = true;
      let valStr = val + "";
      if (valStr.indexOf("-") != -1) {
        //负数
        val = Math.abs(val);
        isPositiveNumber = false;
      }
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        if (!isPositiveNumber) {
          time = "-" + time;
        }
        return time;
      } else {
        return "0秒";
      }
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.backSingleForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];
        objArray.forEach(obj => {
          if (eqpNameArray.indexOf(obj.resName) < 0) {
            eqpNameArray.push(obj.resName);
          }
        });
        form.eqpName = eqpNameArray.join(",");
      };
    },
    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.backSingleForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fdFileArr.indexOf(tag), 1);
            this.backSingleForm.appendix = JSON.stringify(this.fddxFileArr);
            if (this.fddxFileArr.length == 0) {
              this.backSingleForm.attachmentName = null;
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    // 获取告警发生区域默认值
    getFaultRegionDef() {
      apiQueryAlarmDetail({
        pageIndex: 1,
        pageSize: 10,
        param1: JSON.stringify({ woId: this.common.woId }),
      }).then(res => {
        const { alarmProvince = "", alarmRegion = "" } =
          res?.data?.rows?.[0] || {};
        this.backSingleForm.faultRegion = alarmProvince + alarmRegion;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.back-single {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
