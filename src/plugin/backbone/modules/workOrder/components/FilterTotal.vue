<template>
  <div class="filter-total-wrapper">
    <!-- v-if="showArrows" -->
    <i

      class="el-icon-caret-left arrow"
      :class="{ disabled: !canScrollLeft }"
      @click="scrollLeft"
    ></i>
    <div ref="tabsContainer" class="filter-total">
      <div ref="tabs" class="tabs-content">
        <span
          class="filter-total-item"
          :class="activeClassFunc"
          @click="onClickChange()"
          >全部</span
        >
        <template v-for="(item, i) in filters" >
          <span
            :key="i"
            class="filter-total-item"
            :class="{ active: filterVal == item.value }"
            @click="
              onClickChange({
                value: item.value,
                label: item.label,
                professionalType: item.professionalType,
                networkTypeTop: item.networkTypeTop,
              })
            "
            >{{ item.label }}（{{ totals[item.value] || 0 }}）</span
          >
        </template>
      </div>
    </div>
    <!-- v-if="showArrows" -->
    <i

      class="el-icon-caret-right arrow"
      :class="{ disabled: !canScrollRight }"
      @click="scrollRight"
    ></i>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: "FilterTotal",
  props: {
    value: {
      type: [Number, String],
    },
    filters: Array,
    totals: Object,
  },
  data() {
    return {
      filterVal: this.value,
      showArrows: false,
      canScrollLeft: false,
      canScrollRight: false,
    };
  },
  created() {
    console.log(this.totals, "页数");
  },
  computed: {
    ...mapGetters([
      "showAiAgent"
    ]),
    activeClassFunc() {
      return { active: (this.filterVal ?? "") == "" };
    },
  },
  watch: {
    value(newValue) {
      this.filterVal = newValue;
    },
    filters() {
      this.$nextTick(() => {
        this.updateScrollState();
      });
    },
    totals() {
      this.$nextTick(() => {
        this.updateScrollState();
      });
    },
    // 监听智能体状态变化，重新计算宽度
    showAiAgent: {
      handler(newVal, oldVal) {
        console.log('🤖 [FilterTotal] 智能体状态变化:', { oldVal, newVal });
        this.$nextTick(() => {
          this.updateScrollState();
        });
      },
      immediate: false,
    },
  },
  mounted() {
    this.updateScrollState();
    window.addEventListener("resize", this.updateScrollState);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateScrollState);
  },
  methods: {
    onClickChange(val) {
      this.$emit("onConHeadFilterChange", val);
    },
    updateScrollState() {
      this.$nextTick(() => {
        const container = this.$refs.tabsContainer;
        const tabs = this.$refs.tabs;
        if (!container || !tabs) return;

        const containerWidth = container.clientWidth;
        const tabsWidth = tabs.scrollWidth;

        this.showArrows = tabsWidth > containerWidth;
        if (this.showArrows) {
          this.canScrollLeft = container.scrollLeft > 0;
          this.canScrollRight =
            container.scrollLeft < tabsWidth - containerWidth;
        } else {
          this.canScrollLeft = false;
          this.canScrollRight = false;
        }
      });
    },
    scrollLeft() {
      if (!this.canScrollLeft) return;
      const container = this.$refs.tabsContainer;
      const scrollAmount = container.clientWidth * 0.8; // 每次滚动80%的容器宽度
      container.scrollBy({ left: -scrollAmount, behavior: "smooth" });
      // 使用setTimeout确保滚动动画完成后再更新状态
      setTimeout(this.updateScrollState, 666);
    },
    scrollRight() {
      if (!this.canScrollRight) return;
      const container = this.$refs.tabsContainer;
      const scrollAmount = container.clientWidth * 0.8;
      container.scrollBy({ left: scrollAmount, behavior: "smooth" });
      setTimeout(this.updateScrollState, 666);
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-total-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  .arrow {
    font-size: 16px;
    color: #b50b14;
    font-weight: bold;
    cursor: pointer;
    padding: 0 5px;
    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}
.filter-total {
  overflow: hidden;
  flex-grow: 1;
  .tabs-content {
    display: flex;
    white-space: nowrap;
    transition: transform 0.3s;
  }
  .filter-total-item {
    display: inline-block;
    padding: 7px 15px;
    font-size: 13px;
    cursor: pointer;
    flex-shrink: 0; // 防止tab项被压缩
    &.active {
      border-radius: 2px;
      @include themify() {
        background-color: themed("$--color-primary");
        color: themed("$--color-white");
      }
    }
  }
}
</style>
