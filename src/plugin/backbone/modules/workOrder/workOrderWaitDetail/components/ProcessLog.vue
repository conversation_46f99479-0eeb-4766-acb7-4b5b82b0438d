<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">流程日志</span>
    </div>

    <div class="content">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'data', hasdata: 'hasdata' }"
        v-loading="tableLoading"
      >
        <template v-if="!aiAgentActive">
          <el-table-column width="400" prop="opDeptName" label="部门名称">
          <template slot-scope="scope">
            <span v-if="scope.row.processNode != '归档'">
              {{ scope.row.opDeptName }}
            </span>
          </template>
          </el-table-column>
          <el-table-column  prop="processNode" label="环节名称"> </el-table-column>
          <el-table-column  prop="receivedTime" label="到达时间">
          </el-table-column>
          <el-table-column  prop="actionName" label="处理步骤"> </el-table-column>
          <el-table-column  prop="opPerson" label="处理人">
            <template slot-scope="scope">
              <span v-if="scope.row.processNode != '归档'">
                {{ scope.row.opPerson }}
              </span>
            </template>
          </el-table-column>
          <el-table-column  prop="completedTime" label="完成时间">
          </el-table-column>
          <el-table-column  prop="processSuggestion" label="处理意见">
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column v-bind="getColumnProps('opDeptName')" prop="opDeptName" label="部门名称">
          <template slot-scope="scope">
            <span v-if="scope.row.processNode != '归档'">
              {{ scope.row.opDeptName }}
            </span>
          </template>
          </el-table-column>
          <el-table-column v-bind="getColumnProps('processNode')" prop="processNode" label="环节名称"> </el-table-column>
          <el-table-column v-bind="getColumnProps('receivedTime')" prop="receivedTime" label="到达时间">
          </el-table-column>
          <el-table-column v-bind="getColumnProps('actionName')" prop="actionName" label="处理步骤"> </el-table-column>
          <el-table-column v-bind="getColumnProps('opPerson')" prop="opPerson" label="处理人">
            <template slot-scope="scope">
              <span v-if="scope.row.processNode != '归档'">
                {{ scope.row.opPerson }}
              </span>
            </template>
          </el-table-column>
          <el-table-column v-bind="getColumnProps('completedTime')" prop="completedTime" label="完成时间">
          </el-table-column>
          <el-table-column v-bind="getColumnProps('processSuggestion')" prop="processSuggestion" label="处理意见">
          </el-table-column>
        </template>
      </el-table>
    </div>
  </el-card>
</template>

<script>
import { apiGetProcessLog } from "../../api/ProcessLog.js";
export default {
  name: "ProcessLog",
  props: {
    woId: String,
    // 智能体是否展开
    aiAgentActive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
    };
  },
  mounted() {
    this.getLogData();
  },
  methods: {
    getLogData() {
      this.tableLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetProcessLog(param)
        .then(res => {
          if (res.status == 0) {
            let row = res?.data?.rows ?? [];
            if (row.length > 0) {
              row.forEach(el => {
                el.id = this.randomNumber();
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.id = this.randomNumber();
                  });
                }
              });
            }
            this.tableData = row;
            this.tableLoading = false;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    randomNumber() {
      let num = "";
      for (var i = 0; i < 4; i++) {
        num += Math.floor(Math.random() * 10);
      }
      return num;
    },

    // 获取列属性：智能体展开时设置width，关闭时不设置width
    getColumnProps(columnName) {
      if (this.aiAgentActive) {
        // 智能体展开时：使用您调好的固定宽度
        const widthMap = {
          opDeptName: 400,        // 部门名称
          processNode: 100,       // 环节名称
          receivedTime: 200,      // 到达时间
          actionName: 100,        // 处理步骤
          opPerson: 166,          // 处理人
          completedTime: 200,     // 完成时间
          processSuggestion: 300  // 处理意见
        };

        return { width: widthMap[columnName] };
      } else {
        // 智能体关闭时：不设置width，让表格自动适应
        return {};
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
</style>
